.payment-success-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 40px 20px;
}

.success-content {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Success Header */
.success-header {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.success-icon-large {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.success-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.success-subtitle {
  font-size: 18px;
  color: #6b7280;
  margin: 0;
  line-height: 1.6;
}

/* Order Details Card */
.order-details-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24px;
  border-bottom: 2px solid #f3f4f6;
  padding-bottom: 12px;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.status-paid {
  color: #10b981;
  background: #d1fae5;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  width: fit-content;
}

/* Content Details */
.content-details {
  border-top: 1px solid #e5e7eb;
  padding-top: 24px;
}

.content-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.content-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.content-image {
  flex-shrink: 0;
}

.content-thumbnail {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.content-info {
  flex: 1;
}

.content-name {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8px;
  line-height: 1.4;
}

.content-coach {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 4px;
}

.content-type {
  font-size: 14px;
  color: #9ca3af;
  margin: 0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.download-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
  justify-content: center;
}

.download-btn:hover:not(:disabled) {
  background: #059669;
  transform: translateY(-1px);
}

.download-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.btn-outline {
  background: transparent;
  color: #6b7280;
  border: 1px solid #d1d5db;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-outline:hover {
  color: #374151;
  border-color: #9ca3af;
}

.btn-link {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
}

.btn-link:hover {
  color: #2563eb;
}

/* Additional Info */
.additional-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.info-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.info-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.info-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-card li {
  padding: 8px 0;
  color: #6b7280;
  position: relative;
  padding-left: 20px;
}

.info-card li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: bold;
}

.info-card p {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 16px;
}

/* Spinner */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-success-page {
    padding: 20px 16px;
  }
  
  .success-content {
    gap: 24px;
  }
  
  .success-header {
    padding: 32px 16px;
  }
  
  .success-title {
    font-size: 28px;
  }
  
  .success-subtitle {
    font-size: 16px;
  }
  
  .order-details-card {
    padding: 24px 16px;
  }
  
  .order-info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .content-item {
    flex-direction: column;
    text-align: center;
  }
  
  .content-thumbnail {
    width: 80px;
    height: 80px;
    margin: 0 auto;
  }
  
  .additional-info {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    padding: 0 16px;
  }
  
  .download-btn {
    width: 100%;
    min-width: auto;
  }
}

{"success": true, "sheets": {"Sheet1": {"data": [["No.", "PageName", "FigmaLink", "Approve?", "Frontend", "Frontend Ai Tool", "Backend", "Backend AI Tools", "QA"], ["1", "Home page", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=34-585&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", "NO"], ["2", "Signin", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=108-501&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", "Yes"], ["3", "Signup", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=108-590&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", "Yes"], ["4", "Otp-verification", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=219-1077&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", "Yes"], ["5", "Listing page", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=108-105&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", ""], ["6", "Thankyou", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=291-843&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", ""], ["7", "<PERSON><PERSON>", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=435-973&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", "Yes"], ["8", "buyer- Myprofile", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=357-582&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", "Yes"], ["9", "buyer-Mycards", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=357-768&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", ""], ["10", "buyer-addnewcards", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=357-951&t=LQdIyjeAbPiVs1WV-0", "yes", "yes", "Augment", "", "", ""], ["11", "buyer-dasboard", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=357-1116&t=LQdIyjeAbPiVs1WV-0", "NO", "yes", "Augment", "", "", ""], ["12", "buyer-mydownloads", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=357-1116&t=LQdIyjeAbPiVs1WV-0", "NO", "yes", "Augment", "", "", ""], ["13", "buyer-downloadsdetails", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=357-2004&t=LQdIyjeAbPiVs1WV-0", "NO", "No", "Augment", "", "", ""], ["14", "buyer-Myrequest", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=357-1404&t=LQdIyjeAbPiVs1WV-0", "NO", "yes", "Augment", "", "", ""], ["15", "buyer-Mybids", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=357-1704&t=LQdIyjeAbPiVs1WV-0", "NO", "yes", "Augment", "", "", ""], ["16", "buyer-Mydownloads-details", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=357-2004&t=SKjrAn0hvxV1FWNU-0", "yes", "yes", "Augment", "", "", ""], ["17", "seller-dashboard", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=398-258&t=xivXnKLydvWgYdBB-0", "yes", "yes", "Augment", "", "", ""], ["18", "seller-Mysportsstrategy", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=398-1118&t=xivXnKLydvWgYdBB-0", "yes", "yes", "Augment", "", "", ""], ["19", "seller-Mysportsstrategy-details", "https://www.figma.com/design/IGB5SyxC7YmkN5yvJe8qn6/XOsportshub?node-id=398-2175&t=xivXnKLydvWgYdBB-0", "yes", "yes", "Augment", "", "", ""]], "rowCount": 42, "columnCount": 9, "isPreview": true}}, "metadata": {"sheetCount": 2, "sheetNames": ["Sheet1", "Functionality"], "totalRows": 42, "totalCells": 378, "hasFormulas": false, "isPreview": true, "processedSheets": 1}}
/**
 * Text validation utilities for word count, character count, and content validation
 */

/**
 * Count words in text (excluding HTML tags)
 * @param {string} text - Text to count words in
 * @returns {number} Word count
 */
export const countWords = (text) => {
  if (!text || typeof text !== 'string') return 0;
  
  // Remove HTML tags
  const cleanText = text.replace(/<[^>]*>/g, '');
  
  // Remove extra whitespace and split by whitespace
  const words = cleanText.trim().split(/\s+/);
  
  // Return 0 if only empty string
  return words.length === 1 && words[0] === '' ? 0 : words.length;
};

/**
 * Count characters in text (excluding HTML tags)
 * @param {string} text - Text to count characters in
 * @returns {number} Character count
 */
export const countCharacters = (text) => {
  if (!text || typeof text !== 'string') return 0;
  
  // Remove HTML tags and count characters
  const cleanText = text.replace(/<[^>]*>/g, '');
  return cleanText.length;
};

/**
 * Validate word count against limit
 * @param {string} text - Text to validate
 * @param {number} maxWords - Maximum allowed words
 * @returns {object} Validation result
 */
export const validateWordCount = (text, maxWords) => {
  const wordCount = countWords(text);
  
  return {
    isValid: wordCount <= maxWords,
    wordCount,
    maxWords,
    remaining: maxWords - wordCount,
    message: wordCount > maxWords 
      ? `Text exceeds maximum word limit. ${wordCount}/${maxWords} words used.`
      : `${wordCount}/${maxWords} words used.`
  };
};

/**
 * Validate character count against limit
 * @param {string} text - Text to validate
 * @param {number} maxChars - Maximum allowed characters
 * @returns {object} Validation result
 */
export const validateCharacterCount = (text, maxChars) => {
  const charCount = countCharacters(text);
  
  return {
    isValid: charCount <= maxChars,
    charCount,
    maxChars,
    remaining: maxChars - charCount,
    message: charCount > maxChars 
      ? `Text exceeds maximum character limit. ${charCount}/${maxChars} characters used.`
      : `${charCount}/${maxChars} characters used.`
  };
};

/**
 * Get text statistics
 * @param {string} text - Text to analyze
 * @returns {object} Text statistics
 */
export const getTextStats = (text) => {
  const wordCount = countWords(text);
  const charCount = countCharacters(text);
  const charCountWithSpaces = text ? text.replace(/<[^>]*>/g, '').length : 0;
  
  return {
    words: wordCount,
    characters: charCount,
    charactersWithSpaces: charCountWithSpaces,
    paragraphs: text ? text.split(/\n\s*\n/).filter(p => p.trim()).length : 0
  };
};

/**
 * Validation constants for different content types
 */
export const VALIDATION_LIMITS = {
  STRATEGY_DESCRIPTION: {
    maxWords: 2000,
    maxChars: 10000
  },
  ABOUT_COACH: {
    maxWords: 2000,
    maxChars: 10000
  },
  STRATEGIC_CONTENT: {
    maxWords: 2000,
    maxChars: 10000
  },
  TITLE: {
    maxChars: 100
  },
  COACH_NAME: {
    maxChars: 100
  }
};

export default {
  countWords,
  countCharacters,
  validateWordCount,
  validateCharacterCount,
  getTextStats,
  VALIDATION_LIMITS
};

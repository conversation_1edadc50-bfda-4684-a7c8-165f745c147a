/* PowerPoint Document Viewer Styles */
.powerpoint-document-viewer {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  position: relative;
}

/* Header */
.powerpoint-document-viewer__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) var(--basefont);
  background-color: var(--light-gray);
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.powerpoint-document-viewer__info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.powerpoint-document-viewer__title {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
  line-height: 1.2;
}

.powerpoint-document-viewer__type {
  font-size: var(--tinyfont);
  color: #d24726; /* PowerPoint orange color */
  line-height: 1.2;
  font-weight: 500;
}

.powerpoint-document-viewer__stats {
  font-size: var(--tinyfont);
  color: var(--text-muted);
  line-height: 1.2;
}

.powerpoint-document-viewer__download-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--tinyfont) var(--smallfont);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--tinyfont);
  font-size: var(--smallfont);
  transition: all 0.3s ease;
  min-width: 40px;
  height: 36px;
  justify-content: center;
}

.powerpoint-document-viewer__download-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Content area */
.powerpoint-document-viewer__content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--white);
  padding: var(--heading5);
}

.powerpoint-document-viewer__preview-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

/* Icon section */
.powerpoint-document-viewer__icon-section {
  margin-bottom: var(--heading4);
}

.powerpoint-document-viewer__main-icon {
  font-size: 4rem;
  color: #d24726;
  margin-bottom: var(--basefont);
}

.powerpoint-document-viewer__icon-section h3 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.powerpoint-document-viewer__icon-section p {
  margin: 0;
  color: var(--text-muted);
  font-size: var(--smallfont);
}

/* Metadata grid */
.powerpoint-document-viewer__metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--basefont);
  width: 100%;
  margin-bottom: var(--heading4);
}

.powerpoint-metadata-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--basefont);
  background-color: var(--light-gray);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.powerpoint-metadata-icon {
  font-size: var(--heading6);
  color: #d24726;
  flex-shrink: 0;
}

.powerpoint-metadata-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
}

.powerpoint-metadata-label {
  font-size: var(--tinyfont);
  color: var(--text-muted);
  font-weight: 500;
  margin-bottom: 2px;
}

.powerpoint-metadata-value {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
  word-break: break-word;
}

/* Preview notice */
.powerpoint-document-viewer__preview-notice {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: var(--border-radius);
  padding: var(--basefont);
  margin-bottom: var(--heading4);
  text-align: left;
  width: 100%;
}

.powerpoint-document-viewer__preview-notice h4 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--basefont);
  color: #856404;
}

.powerpoint-document-viewer__preview-notice ul {
  margin: 0;
  padding-left: var(--heading5);
  color: #856404;
}

.powerpoint-document-viewer__preview-notice li {
  margin-bottom: var(--tinyfont);
  font-size: var(--smallfont);
  line-height: 1.4;
}

.powerpoint-document-viewer__preview-notice li:last-child {
  margin-bottom: 0;
}

/* Download section */
.powerpoint-document-viewer__download-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--smallfont);
}

.powerpoint-document-viewer__download-button {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--basefont) var(--heading5);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  font-size: var(--basefont);
  font-weight: 500;
  transition: all 0.3s ease;
  text-decoration: none;
}

.powerpoint-document-viewer__download-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.powerpoint-document-viewer__download-section p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--text-muted);
}

/* Loading state */
.powerpoint-document-viewer__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  color: var(--text-muted);
  background-color: var(--light-gray);
}

.powerpoint-document-viewer__loading .spinning {
  font-size: var(--heading5);
  margin-bottom: var(--basefont);
  animation: spin 1s linear infinite;
}

.powerpoint-document-viewer__loading p {
  margin: 0;
  font-size: var(--smallfont);
  text-align: center;
}

.powerpoint-document-viewer__loading-info {
  color: var(--primary-color) !important;
  font-size: var(--tinyfont) !important;
  margin-top: var(--tinyfont) !important;
}

/* Error state */
.powerpoint-document-viewer__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  padding: var(--heading5);
  text-align: center;
  color: var(--text-muted);
  background-color: var(--light-gray);
}

.powerpoint-document-viewer__error svg {
  font-size: var(--heading3);
  color: var(--warning-color);
  margin-bottom: var(--basefont);
}

.powerpoint-document-viewer__error h3 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.powerpoint-document-viewer__error p {
  margin: 0 0 var(--tinyfont) 0;
  font-size: var(--smallfont);
  line-height: 1.4;
}

.powerpoint-document-viewer__error-details {
  color: var(--error-color) !important;
  font-size: var(--tinyfont) !important;
  font-family: monospace !important;
}

/* Responsive design */
@media (max-width: 768px) {
  .powerpoint-document-viewer__header {
    padding: var(--tinyfont) var(--smallfont);
    min-height: 50px;
  }
  
  .powerpoint-document-viewer__title {
    font-size: var(--tinyfont);
  }
  
  .powerpoint-document-viewer__type,
  .powerpoint-document-viewer__stats {
    font-size: 10px;
  }
  
  .powerpoint-document-viewer__download-btn {
    min-width: 35px;
    height: 32px;
    padding: var(--tinyfont);
  }
  
  .powerpoint-document-viewer__content {
    padding: var(--basefont);
  }
  
  .powerpoint-document-viewer__main-icon {
    font-size: 3rem;
  }
  
  .powerpoint-document-viewer__metadata-grid {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }
  
  .powerpoint-metadata-item {
    padding: var(--smallfont);
  }
  
  .powerpoint-document-viewer__preview-notice {
    padding: var(--smallfont);
  }
  
  .powerpoint-document-viewer__download-button {
    padding: var(--smallfont) var(--basefont);
    font-size: var(--smallfont);
  }
  
  .powerpoint-document-viewer__loading,
  .powerpoint-document-viewer__error {
    min-height: 250px;
    padding: var(--basefont);
  }
}

/* Animation for spinning icon */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

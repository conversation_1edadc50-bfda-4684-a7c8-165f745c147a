import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { getOrder } from '../../redux/slices/orderSlice';
import { downloadContent } from '../../redux/slices/orderSlice';
import LoadingSkeleton from '../../components/common/LoadingSkeleton';
import { ErrorDisplay } from '../../components/common/ErrorBoundary';
import { toast } from 'react-toastify';
import '../../styles/PaymentSuccessPage.css';

const PaymentSuccessPage = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  const { user } = useSelector((state) => state.auth);
  const { order, isLoading, error } = useSelector((state) => state.order);

  const [isDownloading, setIsDownloading] = useState(false);

  // Get payment result from navigation state
  const paymentResult = location.state?.paymentResult;
  const orderFromState = location.state?.order;

  useEffect(() => {
    // Check if user is logged in
    if (!user) {
      toast.error('Please log in to view your order');
      navigate('/login');
      return;
    }

    // Fetch order details if not available
    if (orderId && !order) {
      dispatch(getOrder(orderId));
    }
  }, [dispatch, orderId, user, navigate, order]);

  const currentOrder = order || orderFromState;

  const handleDownload = async () => {
    if (!currentOrder) {
      toast.error('Order information not available');
      return;
    }

    setIsDownloading(true);
    try {
      await dispatch(downloadContent(currentOrder._id)).unwrap();
      toast.success('Download started successfully!');
    } catch (error) {
      console.error('Download error:', error);
      toast.error('Failed to download content. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleViewDownloads = () => {
    navigate('/buyer/downloads');
  };

  const handleBackToDashboard = () => {
    navigate('/buyer/dashboard');
  };

  if (isLoading) {
    return <LoadingSkeleton type="payment-success" />;
  }

  if (error || !currentOrder) {
    return (
      <ErrorDisplay
        title="Order Not Found"
        message={error || "Unable to find order information."}
        onRetry={() => navigate('/buyer/dashboard')}
        retryText="Go to Dashboard"
      />
    );
  }

  // Check if order belongs to current user
  if (currentOrder.buyer._id !== user.id && currentOrder.buyer !== user.id) {
    return (
      <ErrorDisplay
        title="Access Denied"
        message="You don't have permission to view this order."
        onRetry={() => navigate('/buyer/dashboard')}
        retryText="Go to Dashboard"
      />
    );
  }

  return (
    <div className="payment-success-page">
      <div className="max-container">
        <div className="success-content">
          {/* Success Header */}
          <div className="success-header">
            <div className="success-icon-large">
              <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
                <circle cx="40" cy="40" r="40" fill="#10B981"/>
                <path d="M25 40L35 50L55 30" stroke="white" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h1 className="success-title">Payment Successful!</h1>
            <p className="success-subtitle">
              Thank you for your purchase. Your payment has been processed successfully.
            </p>
          </div>

          {/* Order Details */}
          <div className="order-details-card">
            <h2 className="card-title">Order Details</h2>
            
            <div className="order-info-grid">
              <div className="info-item">
                <span className="info-label">Order ID</span>
                <span className="info-value">#{currentOrder._id?.slice(-8).toUpperCase()}</span>
              </div>
              
              <div className="info-item">
                <span className="info-label">Date</span>
                <span className="info-value">
                  {new Date(currentOrder.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              </div>
              
              <div className="info-item">
                <span className="info-label">Amount Paid</span>
                <span className="info-value">${currentOrder.amount?.toFixed(2)}</span>
              </div>
              
              <div className="info-item">
                <span className="info-label">Payment Status</span>
                <span className="info-value status-paid">Completed</span>
              </div>
            </div>

            {/* Content Details */}
            <div className="content-details">
              <h3 className="content-title">Purchased Content</h3>
              <div className="content-item">
                <div className="content-image">
                  <img
                    src={currentOrder.content?.thumbnailUrl || "https://via.placeholder.com/100x100/f5f5f5/666666?text=IMG"}
                    alt={currentOrder.content?.title || "Content"}
                    className="content-thumbnail"
                  />
                </div>
                <div className="content-info">
                  <h4 className="content-name">
                    {currentOrder.content?.title || "Content Title"}
                  </h4>
                  <p className="content-coach">
                    By {currentOrder.content?.coachName || "Coach"}
                  </p>
                  <p className="content-type">
                    {currentOrder.content?.contentType || "Digital Content"}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="action-buttons">
            <button
              className="btn-primary download-btn"
              onClick={handleDownload}
              disabled={isDownloading}
            >
              {isDownloading ? (
                <>
                  <span className="spinner"></span>
                  Downloading...
                </>
              ) : (
                <>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  Download Content
                </>
              )}
            </button>

            <button
              className="btn-secondary"
              onClick={handleViewDownloads}
            >
              View All Downloads
            </button>

            <button
              className="btn-outline"
              onClick={handleBackToDashboard}
            >
              Back to Dashboard
            </button>
          </div>

          {/* Additional Info */}
          <div className="additional-info">
            <div className="info-card">
              <h3>What's Next?</h3>
              <ul>
                <li>Your content is now available for download</li>
                <li>You can access it anytime from your Downloads page</li>
                <li>A receipt has been sent to your email</li>
                <li>Contact support if you have any issues</li>
              </ul>
            </div>

            <div className="info-card">
              <h3>Need Help?</h3>
              <p>
                If you have any questions about your purchase or need assistance,
                please don't hesitate to contact our support team.
              </p>
              <button className="btn-link">Contact Support</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;

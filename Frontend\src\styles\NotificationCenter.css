/* Notification Center Styles */
.notification-center {
  position: relative;
  display: inline-block;
}

.notification-trigger {
  position: relative;
  background: none;
  border: none;
  font-size: 20px;
  color: var(--secondary-color);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.notification-trigger:hover {
  background-color: var(--primary-light-color);
  color: var(--btn-color);
}

.notification-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: var(--btn-color);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
  line-height: 1.2;
}

.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 999;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 350px;
  max-height: 500px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--light-gray);
  z-index: 1000;
  overflow: hidden;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--primary-light-color);
}

.notification-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--secondary-color);
}

.notification-actions {
  display: flex;
  gap: 8px;
}

.mark-all-read-btn,
.close-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--dark-gray);
  transition: all 0.2s ease;
}

.mark-all-read-btn:hover,
.close-btn:hover {
  background-color: var(--light-gray);
  color: var(--btn-color);
}

.notification-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: var(--dark-gray);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--light-gray);
  border-top: 2px solid var(--btn-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 16px 20px;
  border-bottom: 1px solid var(--light-gray);
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.notification-item:hover {
  background-color: var(--primary-light-color);
}

.notification-item.unread {
  background-color: #f8f9ff;
  border-left: 3px solid var(--btn-color);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon {
  font-size: 20px;
  margin-right: 12px;
  margin-top: 2px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.notification-message {
  font-size: 13px;
  color: var(--text-color);
  margin: 0 0 6px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 11px;
  color: var(--dark-gray);
}

.notification-item .notification-actions {
  margin-left: 8px;
  flex-shrink: 0;
}

.mark-read-btn {
  background: none;
  border: none;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  color: var(--dark-gray);
  font-size: 12px;
  transition: all 0.2s ease;
}

.mark-read-btn:hover {
  background-color: var(--btn-color);
  color: white;
}

.notification-empty {
  padding: 40px 20px;
  text-align: center;
  color: var(--dark-gray);
}

.notification-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--light-gray);
  background-color: var(--primary-light-color);
}

.view-all-btn {
  width: 100%;
  background: none;
  border: none;
  padding: 8px;
  color: var(--btn-color);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background-color: var(--btn-color);
  color: white;
}

/* Responsive styles */
@media (max-width: 768px) {
  .notification-dropdown {
    width: 300px;
    right: -50px;
  }
}

@media (max-width: 480px) {
  .notification-dropdown {
    width: 280px;
    right: -100px;
  }
  
  .notification-item {
    padding: 12px 16px;
  }
  
  .notification-header {
    padding: 12px 16px;
  }
}
